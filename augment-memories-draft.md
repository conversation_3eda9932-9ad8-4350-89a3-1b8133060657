# User Intent (**CRITICAL - ALWAYS APPLY THESE**)
- When user prepends "planning:" or "question:" to their message, only think about and discuss the question without taking any actions - treat it as information gathering and context building only.
- If a user has used "planning:" or "question:" in a previous session, continue in planning-only mode (no action) until until explicitly told to act.
- "planning:" means that I am preparing to take action, and am trying to build up a context or action; it strongly impacts future messages within this session
- "question:" means that I'm just asking for clarification, or to better understand something you wrote; it does not impact future messages as much, though you may the question and your answer it into consideration
- User prefers candid, accurate information over being told what they want to hear, and wants respectful honesty even when disagreeing with their decisions.
- User plans for all selectors to eventually support replacements and move away from MatchSelector trait in favor of direct TrySelector implementations.
- User is interested in Rust patterns that prevent implicit dropping of objects, requiring explicit handling to avoid forgetting to use extracted values.
- User prefers macro-based approaches for eliminating code duplication, specifically wanting macros that can generate conditional reconstruction logic with placeholder substitution patterns.
- User prefers using a baseline API specification file (like public-api.txt or main branch) as authoritative source for determining public visibility, changing items from pub(crate) to pub only if they appear in the baseline, to ensure explicit and consistent API surfaces.
- User prefers using cargo public-api command output as authoritative source for determining public visibility, changing items from pub to pub(crate) if they don't appear in the public API output.

# Error Handling
- User prefers custom error types (like `SelectionError`) over generic `String` types for better error handling design.
- User prefers `struct` over `enum` for error types when no code branching is needed, valuing type safety, self-documentation, and potential for adding context like originating selector.
- User prefers custom error types to implement the standard `Error` trait (`std::error::Error`).
- User prefers type alias pattern `type Result<T> = std::result::Result<T, ErrorType>` that shadows `std::result::Result` within modules, following conventions like `io::Result<T>`.
- When user asks about renaming types, also check for related types in the same file or module that should be renamed consistently (e.g., if renaming `Selection` to `Select`, also suggest renaming `SelectionError` to `SelectError`).
- User prefers centralizing validation logic at lower levels (like StringMatcher.matches returning Result<bool>) rather than duplicating checks across multiple implementations, following a 'nip things at the bud' approach.
- User prefers to skip difficult-to-create regex evaluation error tests in favor of simpler, more practical test scenarios.

# Code Style & Readability
- User prefers creating type aliases for complex Result types (e.g., `crate::select::Error` for `Result<Selection, SelectionError>`) to improve code readability and maintainability.
- User prefers adding imports instead of using fully qualified paths like `crate::select::api::Result` or `crate::select::api::Selection` directly in code.
- User prefers `Hit`/`Miss` variant names for selection enums, avoids `Match` enum name due to conflict with `Matcher` type.
- User prefers associated constants (const NAME) over associated functions (fn name()) for trait-level metadata like names.
- `Select::Hit` is designed to allow modifying items during selection since `TrySelector::try_select` takes owned values, and `Select::Miss` exists specifically to return ownership when no match occurs; `TableSelector` already demonstrates this mutation pattern.
- When designing traits that need to conditionally modify items, avoid `fn try_method(item: I) -> Result<Option<I>, Error>` because `Ok(None)` loses ownership of the original item; prefer either returning `Select` directly or using `&mut I` with boolean return and contract that item is unmodified on `false`.
- User prefers avoiding helper traits when they don't add significant value beyond error mapping, and suggests implementing core traits directly instead of creating near-duplicate abstractions.
- When refactoring a struct to an enum with struct-like variants, existing code typically maps to one specific variant and can often be fixed by appending the variant name (e.g., `Link` becomes `Link::Standard`).

# Testing
- User prefers testing both `matches` and `try_select` methods in selector tests to ensure error propagation includes selector names correctly, even when current implementation has TODO panics.
- User prefers testing error results by directly comparing `result == Err(SpecificError)` rather than using `result.is_err()` followed by `result.unwrap_err()` and separate error variant checking.
- User prefers inlining variables in tests and using direct `assert_eq!(result, Err(SpecificError))` rather than separate variable assignment and assertion.
- User prefers comprehensive error handling tests for all selector implementations that verify both `matches()` and `try_select()` error propagation, with each selector implementing its own `name()` method and removing default `todo!()` implementations to force explicit naming.
- User prefers shorter test names: 'match_error' instead of 'propagates_string_match_error' for error handling tests.
- For mdq project tests in tests/md_cases/, use search-replace syntax `!s/original/replacement/` and test against various markdown elements like sections, code blocks, lists with formatting, images, and nested formatting.
- User prefers test cases with short, self-describing text over real-world examples when there's a trade-off, prioritizing clarity and obviousness of what each test case is testing.
- User prefers creating tests similar to `image_url_replaced_but_alt_does_not_match` pattern in tests/md_cases/select_link.toml for testing URL replacement scenarios where one field matches but another doesn't.

# Git Commits
- For git commits, keep the first line to 50-70 chars max (preferring 50), followed by a blank line, then the rest of the body hard-wrapped to 72 chars.
- When writing commit messages, avoid redundant explanations of self-evident consequences.
- For commit messages: surround types with backticks, don't wrap types across lines, use 'the `Selection` enum' instead of '`Selection` enum', and emphasize future capabilities over past problems when describing refactoring benefits.

# Pull Requests
- User prefers much shorter PR descriptions - just a short paragraph, maybe two at most.
- When writing PR descriptions, focus on the fundamental change (the initial tug of the thread) rather than describing all the cascading consequences that followed from that core change.

# String Matching and Replacement
- For link/image URL replacement, StringMatcher should have match_replace(&mut str) -> Result<bool> method since URLs are plain strings, not Vec<Inline>, and the method should return true if there was a match regardless of replacement.
- For LinkSelector and ImageSelector, URL replacement using match_replace should only be applied to URL fields, not to display text or alt text fields.

# mdq Project Workflow
- For mdq project workflows, use `docker pull yshavit/mdq` and `echo "$PR_DESCRIPTION" | docker run --rm -i yshavit/mdq -q '# Breaking change'` to validate that PR descriptions contain required sections.
- For GitHub Actions multiline outputs, prefer using random delimiters (like `uuidgen | tr -d -`) instead of `EOF` to avoid conflicts with user content.
- For GitHub Actions multiline outputs, prefer using environment variables in steps rather than inlining with ${{ steps.step-id.outputs.output-name }} syntax.
- User prefers placing here-string syntax (`<<<`) before the command rather than after it in shell scripts.
